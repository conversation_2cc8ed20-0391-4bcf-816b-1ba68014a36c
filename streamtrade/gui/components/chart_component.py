"""
Chart component for Lionaire GUI.
"""

import streamlit as st
import plotly.graph_objects as go
from typing import Optional, Dict, Any

from ...config.logging_config import get_logger
from ...config.strings import get_string, strings
from ...visualization.chart_viewer import ChartViewer

logger = get_logger(__name__)


class ChartComponent:
    """
    Chart display component with interactive features.
    """
    
    def __init__(self, chart_viewer: ChartViewer):
        self.chart_viewer = chart_viewer
    
    def render(self) -> Optional[go.Figure]:
        """
        Render the main chart component.
        
        Returns:
            Plotly Figure object if chart is created, None otherwise
        """
        st.subheader("📈 Price Chart")
        
        # Check if data is loaded
        if not hasattr(st.session_state, 'data_loaded') or not st.session_state.data_loaded:
            st.info("Please load data first using the Data Selection panel.")
            return None
        
        # Chart controls
        self._render_chart_controls()
        
        # Create or get chart
        fig = self._get_or_create_chart()
        
        if fig:
            # Display chart with enhanced interactivity
            st.plotly_chart(
                fig,
                use_container_width=True,
                config={
                    'displayModeBar': True,
                    'displaylogo': False,
                    'scrollZoom': True,  # Enable mouse wheel zoom
                    'doubleClick': 'reset',  # Double-click to reset zoom
                    'showTips': True,  # Show tooltips
                    'toImageButtonOptions': {
                        'format': 'png',
                        'filename': f"{st.session_state.current_pair}_{st.session_state.current_timeframe}_chart",
                        'height': 600,
                        'width': 1200,
                        'scale': 2
                    },
                    # Use default mode bar with all standard buttons including fullscreen
                    'modeBarButtonsToRemove': [
                        'lasso2d',  # Tidak perlu untuk trading
                        'select2d',  # Tidak perlu untuk trading
                        'sendDataToCloud',  # Privacy concern
                        'editInChartStudio'  # Tidak perlu
                    ],  # Only remove selection tools
                    # Enable fullscreen mode
                    'showEditInChartStudio': False,
                    'plotlyServerURL': "https://chart-studio.plotly.com"
                }
            )
            
            # Chart information
            self._render_chart_info()
            
            return fig
        else:
            st.error("Failed to create chart. Please check your data and try again.")
            return None
    
    def _render_chart_controls(self):
        """Render chart control buttons."""
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            if st.button("🔄 Refresh Chart", help="Reload data and refresh chart"):
                with st.spinner("Refreshing chart..."):
                    fig = self.chart_viewer.refresh_chart()
                    if fig:
                        st.session_state.chart_figure = fig
                        st.success("Chart refreshed!")
                        st.rerun()
                    else:
                        st.error("Failed to refresh chart")
        
        with col2:
            if st.button("🎯 Auto Scale", help="Auto-scale chart to fit data"):
                if hasattr(st.session_state, 'chart_figure'):
                    # Update layout to auto-scale
                    fig = st.session_state.chart_figure
                    fig.update_layout(
                        xaxis=dict(autorange=True),
                        yaxis=dict(autorange=True)
                    )
                    st.session_state.chart_figure = fig
                    st.rerun()
        
        with col3:
            # Manage Indicators button
            if st.button("🔧 Manage Indicators", help="Open indicator management panel"):
                st.session_state.show_indicator_panel = True
                st.rerun()
        
        with col4:
            # Timeframe selector
            available_timeframes = self.chart_viewer.get_available_timeframes()
            current_timeframe = getattr(st.session_state, 'current_timeframe', 'H1')

            # Find current index
            try:
                current_index = available_timeframes.index(current_timeframe)
            except ValueError:
                current_index = 0

            selected_timeframe = st.selectbox(
                label="Timeframe",
                options=available_timeframes,
                index=current_index,
                key="chart_timeframe",
                label_visibility="collapsed",
                help="Select chart timeframe"
            )

            # Handle timeframe change (optimized)
            if selected_timeframe != current_timeframe:
                with st.spinner(f"Switching to {selected_timeframe}..."):
                    # Update session state immediately to prevent re-triggering
                    st.session_state.current_timeframe = selected_timeframe

                    # Change timeframe and get new chart
                    fig = self.chart_viewer.change_timeframe(selected_timeframe)
                    if fig:
                        st.session_state.chart_figure = fig

                        # Save persistent chart state for timeframe switch
                        try:
                            from pathlib import Path
                            import json
                            from datetime import datetime

                            if hasattr(st.session_state, 'last_selected_pair') and st.session_state.last_selected_pair:
                                config_dir = Path(__file__).parent.parent.parent / 'config'
                                config_dir.mkdir(exist_ok=True)
                                state_file = config_dir / 'last_chart_state.json'

                                chart_info = self.chart_viewer.get_chart_info()
                                state = {
                                    'pair': st.session_state.last_selected_pair,
                                    'timeframe': selected_timeframe,
                                    'timestamp': datetime.now().isoformat(),
                                    'data_points': chart_info.get('data_points', 0),
                                    'date_range': chart_info.get('date_range', {}),
                                    'loading_method': 'timeframe_switch',
                                    'version': '1.0.0'
                                }

                                with open(state_file, 'w', encoding='utf-8') as f:
                                    json.dump(state, f, indent=2)

                                logger.debug(f"Saved chart state after timeframe switch: {selected_timeframe}")

                        except Exception as e:
                            logger.debug(f"Could not save chart state after timeframe switch: {e}")

                        # Use toast for less intrusive feedback
                        st.toast(f"✅ Switched to {selected_timeframe}", icon="⚡")
                        st.rerun()
                    else:
                        # Revert timeframe on failure
                        st.session_state.current_timeframe = current_timeframe
                        st.error(f"Failed to switch to {selected_timeframe}")

        with col5:
            # Chart size options
            size_options = {
                "Small": (800, 400),
                "Medium": (1000, 600),
                "Large": (1200, 800)
            }

            selected_size = st.selectbox(
                label="Chart Size",
                options=list(size_options.keys()),
                index=1,
                key="chart_size",
                label_visibility="collapsed"
            )

            if st.session_state.get('previous_size') != selected_size:
                st.session_state.previous_size = selected_size
                if hasattr(st.session_state, 'chart_figure'):
                    width, height = size_options[selected_size]
                    fig = st.session_state.chart_figure
                    fig.update_layout(width=width, height=height)
                    st.session_state.chart_figure = fig
                    st.rerun()
    
    def _get_or_create_chart(self) -> Optional[go.Figure]:
        """Get existing chart or create new one."""
        try:
            # Check if we have a cached chart
            if hasattr(st.session_state, 'chart_figure'):
                return st.session_state.chart_figure
            
            # Create new chart with default settings
            with st.spinner("Creating chart..."):
                # Use default remove_gaps=True (now set in chart_viewer)
                fig = self.chart_viewer.create_chart()

                if fig:
                    st.session_state.chart_figure = fig
                    # Initialize default settings in session state
                    if not hasattr(st.session_state, 'chart_remove_gaps'):
                        st.session_state.chart_remove_gaps = True
                    if not hasattr(st.session_state, 'chart_candlestick_style'):
                        st.session_state.chart_candlestick_style = "Candlestick"
                    if not hasattr(st.session_state, 'chart_show_vertical_line'):
                        st.session_state.chart_show_vertical_line = True
                    if not hasattr(st.session_state, 'chart_show_horizontal_line'):
                        st.session_state.chart_show_horizontal_line = True
                    return fig
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"Error creating chart: {str(e)}")
            st.error(f"Chart creation failed: {str(e)}")
            return None
    
    def _render_chart_info(self):
        """Render chart information and statistics."""
        chart_info = self.chart_viewer.get_chart_info()
        
        with st.expander("📊 Chart Information", expanded=False):
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.write("**Data Information**")
                st.write(f"Pair: {chart_info['pair']}")
                st.write(f"Timeframe: {chart_info['timeframe']}")
                st.write(f"Data Points: {chart_info['data_points']:,}")

                # Show loading limits info
                data_info = self.chart_viewer.get_data_info()
                if data_info and 'loading_limits' in data_info:
                    limits = data_info['loading_limits']
                    max_load = limits.get('max_candles_load', 0)
                    max_display = limits.get('max_candles_display', 0)
                    loaded = chart_info['data_points']

                    # Check if we hit limits
                    hit_load_limit = loaded >= max_load if max_load > 0 else False
                    hit_display_limit = loaded >= max_display if max_display > 0 else False

                    if hit_load_limit:
                        st.write(f"⚠️ Hit load limit: {max_load:,}")
                    elif hit_display_limit:
                        st.write(f"⚠️ Hit display limit: {max_display:,}")
                    else:
                        st.write(f"✅ Within limits ({max_load:,} max)")

            with col2:
                st.write("**Date Range**")
                if chart_info['date_range']['start']:
                    st.write(f"Start: {chart_info['date_range']['start'].strftime('%Y-%m-%d %H:%M')}")
                if chart_info['date_range']['end']:
                    st.write(f"End: {chart_info['date_range']['end'].strftime('%Y-%m-%d %H:%M')}")

                # Calculate duration
                if chart_info['date_range']['start'] and chart_info['date_range']['end']:
                    duration = chart_info['date_range']['end'] - chart_info['date_range']['start']
                    st.write(f"Duration: {duration.days} days")

            with col3:
                st.write("**Indicators**")
                st.write(f"Active: {chart_info['indicator_count']}")
                if chart_info['indicators']:
                    for indicator in chart_info['indicators']:
                        st.write(f"• {indicator}")
                else:
                    st.write("No indicators active")

            with col4:
                st.write("**Cache Information**")
                cache_info = self.chart_viewer.get_cache_info()
                st.write(f"Disk Cache: {cache_info['disk_cache_entries']} entries")
                st.write(f"Memory Cache: {cache_info['memory_cache_entries']} entries")
                st.write(f"Cache Size: {cache_info['total_cache_size_mb']:.1f} MB")

                # Clear cache button
                if st.button("🗑️ Clear Cache", help="Clear all cached data", use_container_width=True):
                    success = self.chart_viewer.clear_all_cache()
                    if success:
                        st.success("✅ Cache cleared successfully!")
                        st.rerun()
                    else:
                        st.error("❌ Failed to clear cache")
    
    def render_chart_settings(self):
        """Render advanced chart settings."""
        st.subheader("⚙️ Chart Settings")

        with st.expander("Advanced Settings", expanded=False):
            col1, col2 = st.columns(2)

            with col1:
                st.write("**Display Options**")

                show_volume = st.checkbox(
                    "Show Volume",
                    value=True,
                    help="Display volume bars below price chart"
                )

                show_grid = st.checkbox(
                    "Show Grid",
                    value=True,
                    help="Display grid lines on chart"
                )

                st.write("**Crosshair Options**")
                show_vertical_line = st.checkbox(
                    "Show Vertical Line",
                    value=getattr(st.session_state, 'chart_show_vertical_line', True),
                    help="Display vertical crosshair line"
                )

                show_horizontal_line = st.checkbox(
                    "Show Horizontal Line",
                    value=getattr(st.session_state, 'chart_show_horizontal_line', True),
                    help="Display horizontal crosshair line with price level"
                )

                remove_gaps = st.checkbox(
                    "Remove Weekend Gaps",
                    value=True,
                    help="Remove gaps in chart during weekends/holidays for continuous display"
                )

            with col2:
                st.write("**Price Display**")

                price_precision = st.number_input(
                    "Price Precision",
                    min_value=0,
                    max_value=8,
                    value=4,
                    help="Number of decimal places for price display"
                )

                # Get current style from session state or default
                current_style = getattr(st.session_state, 'chart_candlestick_style', "Candlestick")
                style_options = ["Candlestick", "OHLC Bars", "Line"]
                current_index = style_options.index(current_style) if current_style in style_options else 0

                candlestick_style = st.selectbox(
                    "Candlestick Style",
                    options=style_options,
                    index=current_index,
                    help="Chart display style"
                )
            
            # Apply settings button
            if st.button("Apply Settings"):
                if hasattr(st.session_state, 'chart_figure'):
                    # Store settings in session state
                    st.session_state.chart_remove_gaps = remove_gaps
                    st.session_state.chart_show_grid = show_grid
                    st.session_state.chart_price_precision = price_precision
                    st.session_state.chart_show_vertical_line = show_vertical_line
                    st.session_state.chart_show_horizontal_line = show_horizontal_line
                    st.session_state.chart_candlestick_style = candlestick_style

                    # Recreate chart with new settings
                    fig = self.chart_viewer.create_chart(
                        remove_gaps=remove_gaps,
                        chart_style=candlestick_style,
                        show_vertical_line=show_vertical_line,
                        show_horizontal_line=show_horizontal_line
                    )

                    if fig:
                        # Apply additional settings
                        fig.update_layout(
                            xaxis=dict(showgrid=show_grid),
                            yaxis=dict(showgrid=show_grid, tickformat=f'.{price_precision}f'),
                            hovermode='x unified' if (show_vertical_line or show_horizontal_line) else 'closest'
                        )

                        st.session_state.chart_figure = fig
                        st.success("Settings applied!")
                        st.rerun()
    
    def render_chart_analysis(self):
        """Render basic chart analysis tools."""
        if not hasattr(st.session_state, 'data_loaded') or not st.session_state.data_loaded:
            return
        
        st.subheader("🔍 Quick Analysis")
        
        try:
            # Get current data for analysis
            chart_info = self.chart_viewer.get_chart_info()
            
            if chart_info['data_points'] > 0:
                # Get the actual data
                current_data = self.chart_viewer.current_data
                
                if current_data is not None and not current_data.empty:
                    # Detailed statistics with moved metrics
                    with st.expander("📈 Detailed Statistics"):
                        # Quick metrics section
                        st.write("**Quick Metrics**")
                        col1, col2, col3, col4 = st.columns(4)

                        with col1:
                            current_price = current_data['close'].iloc[-1]
                            st.metric(
                                "Current Price",
                                f"{current_price:.{4}f}",
                                help="Latest closing price"
                            )

                        with col2:
                            price_change = current_data['close'].iloc[-1] - current_data['close'].iloc[-2] if len(current_data) > 1 else 0
                            price_change_pct = (price_change / current_data['close'].iloc[-2] * 100) if len(current_data) > 1 and current_data['close'].iloc[-2] != 0 else 0

                            st.metric(
                                "Change",
                                f"{price_change:+.{4}f}",
                                f"{price_change_pct:+.2f}%",
                                help="Price change from previous candle"
                            )

                        with col3:
                            high_24h = current_data['high'].tail(24).max() if len(current_data) >= 24 else current_data['high'].max()
                            st.metric(
                                "24h High",
                                f"{high_24h:.{4}f}",
                                help="Highest price in last 24 periods"
                            )

                        with col4:
                            low_24h = current_data['low'].tail(24).min() if len(current_data) >= 24 else current_data['low'].min()
                            st.metric(
                                "24h Low",
                                f"{low_24h:.{4}f}",
                                help="Lowest price in last 24 periods"
                            )

                        st.divider()

                        # Additional detailed statistics
                        col1, col2 = st.columns(2)

                        with col1:
                            st.write("**Price Statistics**")
                            st.write(f"Open: {current_data['open'].iloc[0]:.{4}f}")
                            st.write(f"High: {current_data['high'].max():.{4}f}")
                            st.write(f"Low: {current_data['low'].min():.{4}f}")
                            st.write(f"Close: {current_data['close'].iloc[-1]:.{4}f}")

                        with col2:
                            st.write("**Volume Statistics**")
                            if 'volume' in current_data.columns:
                                st.write(f"Total Volume: {current_data['volume'].sum():,.0f}")
                                st.write(f"Avg Volume: {current_data['volume'].mean():,.0f}")
                                st.write(f"Max Volume: {current_data['volume'].max():,.0f}")
                            else:
                                st.write("Volume data not available")
        
        except Exception as e:
            logger.error(f"Error in chart analysis: {str(e)}")
            st.error("Unable to perform chart analysis")

"""
Main Streamlit application for Lionaire platform.
"""

import streamlit as st
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from streamtrade.config.logging_config import setup_logging, get_logger
from streamtrade.config.settings import settings
from streamtrade.config.strings import get_string, strings
from streamtrade.visualization.chart_viewer import ChartViewer
from streamtrade.gui.components.data_selector import DataSelector
from streamtrade.gui.components.chart_component import ChartComponent
from streamtrade.gui.components.indicator_panel import IndicatorPanel
from streamtrade.gui.components.simple_settings_panel import SimpleSettingsPanel

# Setup logging
setup_logging(level="INFO", console_output=False)
logger = get_logger(__name__)


class LionaireApp:
    """
    Main Lionaire Streamlit application.

    Features:
    - Data selection and loading
    - Interactive chart display
    - Technical indicator management
    - Real-time chart updates
    - Export capabilities
    """
    
    def __init__(self):
        self.setup_page_config()
        self.initialize_components()
        
    def setup_page_config(self):
        """Configure Streamlit page settings."""
        st.set_page_config(
            page_title=settings.gui_settings["page_title"],
            page_icon=settings.gui_settings["page_icon"],
            layout=settings.gui_settings["layout"],
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS for better styling
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .metric-container {
            background-color: #f0f2f6;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem 0;
        }
        
        .sidebar .sidebar-content {
            background-color: #f8f9fa;
        }
        
        .stButton > button {
            width: 100%;
        }
        
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 0.75rem;
            border-radius: 0.25rem;
            border: 1px solid #c3e6cb;
        }
        
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 0.25rem;
            border: 1px solid #f5c6cb;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_components(self):
        """Initialize application components."""
        try:
            # Initialize chart viewer (this will also initialize data manager)
            if 'chart_viewer' not in st.session_state:
                st.session_state.chart_viewer = ChartViewer()

            self.chart_viewer = st.session_state.chart_viewer

            # Initialize persistent state for better UX
            if 'last_selected_pair' not in st.session_state:
                st.session_state.last_selected_pair = None
            if 'last_selected_timeframe' not in st.session_state:
                st.session_state.last_selected_timeframe = None
            if 'last_loaded_data' not in st.session_state:
                st.session_state.last_loaded_data = None
            if 'last_chart_state' not in st.session_state:
                st.session_state.last_chart_state = None
            if 'current_timeframe' not in st.session_state:
                st.session_state.current_timeframe = None

            # Load persistent chart state from file
            self._load_persistent_chart_state()

            # Try to restore last chart from cache if available
            self._try_restore_last_chart()
            
            # Initialize GUI components
            self.data_selector = DataSelector(self.chart_viewer)
            self.chart_component = ChartComponent(self.chart_viewer)
            self.indicator_panel = IndicatorPanel(self.chart_viewer)
            self.settings_panel = SimpleSettingsPanel()
            
            logger.info("StreamTrade app components initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing components: {str(e)}")
            st.error(f"Failed to initialize application: {str(e)}")

    def _load_persistent_chart_state(self):
        """Load persistent chart state from file."""
        try:
            from pathlib import Path
            import json

            # Use project-local state file
            state_file = Path(__file__).parent.parent / 'config' / 'last_chart_state.json'

            if state_file.exists():
                try:
                    with open(state_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()

                    if content:
                        state = json.loads(content)

                        # Validate required fields
                        if state.get('pair') and state.get('timeframe'):
                            # Restore state to session
                            st.session_state.last_selected_pair = state.get('pair')
                            st.session_state.last_selected_timeframe = state.get('timeframe')
                            st.session_state.current_timeframe = state.get('timeframe')
                            st.session_state.last_chart_state = state

                            logger.info(f"✅ Loaded persistent chart state: {state.get('pair')} {state.get('timeframe')}")
                            return True
                        else:
                            logger.debug("Invalid state file: missing required fields")
                    else:
                        logger.debug("Empty state file")

                except json.JSONDecodeError as e:
                    logger.warning(f"Corrupted state file, removing: {e}")
                    state_file.unlink()  # Remove corrupted file
                except Exception as e:
                    logger.debug(f"Error reading state file: {e}")
            else:
                logger.debug("No persistent state file found")

        except Exception as e:
            logger.debug(f"Could not load persistent chart state: {e}")

        return False

    def _save_persistent_chart_state(self, pair: str, timeframe: str, additional_data: dict = None):
        """Save current chart state to persistent file."""
        try:
            from pathlib import Path
            import json
            from datetime import datetime

            # Use project-local state file
            config_dir = Path(__file__).parent.parent / 'config'
            config_dir.mkdir(exist_ok=True)
            state_file = config_dir / 'last_chart_state.json'

            state = {
                'pair': pair,
                'timeframe': timeframe,
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            }

            # Add additional data if provided
            if additional_data:
                state.update(additional_data)

            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2)

            # Update session state
            st.session_state.last_chart_state = state

            logger.debug(f"Saved persistent chart state: {pair} {timeframe}")

        except Exception as e:
            logger.debug(f"Could not save persistent chart state: {e}")

    def _try_restore_last_chart(self):
        """Enhanced chart restoration from cache with better error handling."""
        try:
            # Check if we have persistent state to restore
            if not (st.session_state.last_selected_pair and st.session_state.last_selected_timeframe):
                logger.debug("No persistent chart state to restore")
                return False

            pair = st.session_state.last_selected_pair
            timeframe = st.session_state.last_selected_timeframe

            logger.info(f"🔄 Attempting to restore chart: {pair} {timeframe}")

            # Try multiple restoration strategies

            # Strategy 1: Try to get data from cache
            data_manager = self.chart_viewer.data_manager
            if hasattr(data_manager, 'get_cached_data'):
                logger.debug("Strategy 1: Trying cached data restoration")
                cached_data = data_manager.get_cached_data(pair, timeframe)
                if cached_data is not None and not cached_data.empty:
                    # Restore data to chart viewer
                    self.chart_viewer.current_data = cached_data
                    self.chart_viewer.current_pair = pair
                    self.chart_viewer.current_timeframe = timeframe

                    # Create chart
                    fig = self.chart_viewer.create_chart()
                    if fig:
                        st.session_state.chart_figure = fig
                        st.session_state.data_loaded = True
                        st.session_state.current_timeframe = timeframe

                        logger.info(f"✅ Successfully restored chart from cache: {pair} {timeframe} ({len(cached_data)} candles)")
                        return True

            # Strategy 2: Try loading with default parameters (5 days back)
            logger.debug("Strategy 2: Trying fallback data loading (5 days)")
            success = self.chart_viewer.load_n_days_back(pair, timeframe, 5)
            if success:
                fig = self.chart_viewer.create_chart()
                if fig:
                    st.session_state.chart_figure = fig
                    st.session_state.data_loaded = True
                    st.session_state.current_timeframe = timeframe

                    logger.info(f"✅ Successfully restored chart with 5-day loading: {pair} {timeframe}")
                    return True

            # Strategy 3: Try with smaller data set (2 days back)
            logger.debug("Strategy 3: Trying smaller data set (2 days)")
            success = self.chart_viewer.load_n_days_back(pair, timeframe, 2)
            if success:
                fig = self.chart_viewer.create_chart()
                if fig:
                    st.session_state.chart_figure = fig
                    st.session_state.data_loaded = True
                    st.session_state.current_timeframe = timeframe

                    logger.info(f"✅ Successfully restored chart with 2-day loading: {pair} {timeframe}")
                    return True

            # Strategy 4: Try with last N candles approach
            logger.debug("Strategy 4: Trying last N candles approach")
            try:
                success = self.chart_viewer.load_last_n_candles(pair, timeframe, 1000)
                if success:
                    fig = self.chart_viewer.create_chart()
                    if fig:
                        st.session_state.chart_figure = fig
                        st.session_state.data_loaded = True
                        st.session_state.current_timeframe = timeframe

                        logger.info(f"✅ Successfully restored chart with last N candles: {pair} {timeframe}")
                        return True
            except:
                pass  # Method might not exist

            logger.warning(f"❌ Could not restore chart data for {pair} {timeframe} with any strategy")
            return False

        except Exception as e:
            logger.error(f"Could not restore last chart: {e}")
            return False

    def render_header(self):
        """Render application header."""
        st.markdown(f'<h1 class="main-header">{strings.APP["title"]}</h1>', unsafe_allow_html=True)

        st.markdown(f"""
        <div style="text-align: center; margin-bottom: 2rem; color: #666;">
            {strings.APP["subtitle"]}
        </div>
        """, unsafe_allow_html=True)
        
        # Statistics bar removed - information available in chart details below
    
    def render_sidebar(self):
        """Render sidebar with data selection and controls."""
        with st.sidebar:
            st.header("🎛️ Control Panel")
            
            # Data selection
            with st.expander("📊 Data Selection", expanded=True):
                self.data_selector.render()
            
            # Indicator management
            with st.expander("📈 Indicators", expanded=False):
                self.indicator_panel.render_indicator_summary()
                
                if st.button("🔧 Manage Indicators"):
                    st.session_state.show_indicator_panel = True

            # Settings panel
            with st.expander("⚙️ Platform Settings", expanded=False):
                if st.button("⚙️ Settings"):
                    st.session_state.show_settings_panel = True
                    st.rerun()

                # Settings summary table
                st.markdown("**Current Settings**")
                self.settings_panel.render_settings_summary_sidebar()

            # System information
            with st.expander("ℹ️ System Info", expanded=False):
                self.render_system_info()
            
            # Help section
            with st.expander("❓ Help", expanded=False):
                self.render_help()
    
    def render_main_content(self):
        """Render main content area."""
        # Check which panel should be shown
        if st.session_state.get('show_settings_panel', False):
            self.render_settings_management()
        elif st.session_state.get('show_indicator_panel', False):
            self.render_indicator_management()
        else:
            self.render_chart_view()
    
    def render_chart_view(self):
        """Render main chart view."""
        # Chart display
        fig = self.chart_component.render()
        
        # Chart controls and analysis
        if fig:
            col1, col2 = st.columns([1, 1])  # 50:50 ratio for equal width

            with col1:
                self.chart_component.render_chart_settings()

            with col2:
                self.chart_component.render_chart_analysis()
        
        # Data information
        if hasattr(st.session_state, 'data_loaded') and st.session_state.data_loaded:
            with st.expander("📋 Data Information", expanded=False):
                self.data_selector.render_data_info()
                self.data_selector.render_export_options()
    
    def render_indicator_management(self):
        """Render indicator management interface."""
        col1, col2 = st.columns([4, 1])

        with col1:
            st.subheader("📊 Indicator Management")  # Changed from header to subheader for alignment

        with col2:
            if st.button("⬅️ Back to Chart"):
                st.session_state.show_indicator_panel = False
                st.rerun()
        
        # Render indicator panel
        self.indicator_panel.render()

    def render_settings_management(self):
        """Render settings management interface."""
        # Render settings panel (SimpleSettingsPanel handles its own header)
        self.settings_panel.render()

    def render_system_info(self):
        """Render system information."""
        try:
            # Use EnhancedDataManager from chart_viewer
            data_manager = self.chart_viewer.data_manager

            # Memory usage - bypass get_cache_info() and access directly
            st.write("**Memory Cache Information**")
            try:
                # Access cache data directly to avoid indicator_cache error
                with data_manager._cache_lock:
                    data_cache_size = sum(item['size_mb'] for item in data_manager._data_cache.values())
                    m1_cache_size = sum(item['size_mb'] for item in data_manager._m1_base_cache.values())
                    total_memory_cache_size = data_cache_size + m1_cache_size

                    st.write(f"Data Entries: {len(data_manager._data_cache)}")
                    st.write(f"M1 Entries: {len(data_manager._m1_base_cache)}")
                    st.write(f"Total Size: {total_memory_cache_size:.2f} MB")

                    if hasattr(data_manager, 'max_cache_size_mb') and data_manager.max_cache_size_mb > 0:
                        usage_percent = (total_memory_cache_size / data_manager.max_cache_size_mb) * 100
                        st.write(f"Usage: {usage_percent:.1f}%")
                    else:
                        st.write("Usage: N/A")
            except Exception as e:
                st.info("ℹ️ Memory cache information not available")

            # Disk cache - access directly
            st.write("**Disk Cache Information**")
            try:
                if hasattr(data_manager, 'disk_cache') and data_manager.disk_cache:
                    # Access disk cache stats directly
                    disk_stats = data_manager.disk_cache.get_stats()
                    st.write(f"Total Entries: {disk_stats.get('total_entries', 0)}")
                    st.write(f"Total Size: {disk_stats.get('total_size_mb', 0):.2f} MB")
                    st.write(f"Cache Directory: {disk_stats.get('cache_dir', 'N/A')}")
                else:
                    st.info("ℹ️ Disk cache not available or disabled")
            except Exception as e:
                st.info("ℹ️ Disk cache information not available")

            # Indicator Cache Information - completely separate
            st.write("**Indicator Cache Information**")
            # Always show the not implemented message for now
            st.info("ℹ️ Indicator cache statistics not yet implemented")

            # Application info - always show
            st.write("**Application**")
            st.write(f"Version: 0.5.4+")
            st.write(f"Phase: 5.4+ (Enhanced)")

            # Clear cache button - fixed implementation
            if st.button("🗑️ Clear Cache", key="clear_cache_btn"):
                try:
                    # Clear memory cache directly
                    with data_manager._cache_lock:
                        data_manager._data_cache.clear()
                        data_manager._m1_base_cache.clear()
                        if hasattr(data_manager, '_user_context'):
                            data_manager._user_context.clear()

                    # Clear disk cache if available
                    if hasattr(data_manager, 'disk_cache') and data_manager.disk_cache:
                        try:
                            data_manager.disk_cache.clear_cache()
                        except:
                            pass  # Ignore disk cache clear errors

                    # Clear session converter cache if available
                    if hasattr(data_manager, 'session_converter') and data_manager.session_converter:
                        try:
                            data_manager.session_converter.clear_cache()
                        except:
                            pass  # Ignore session converter clear errors

                    st.success("✅ Cache cleared successfully!")
                    st.rerun()

                except Exception as e:
                    st.warning(f"⚠️ Partial cache clear: {str(e)}")
                    st.rerun()

        except Exception as e:
            # Only show this if everything fails
            st.info("ℹ️ System information temporarily unavailable")

            # Still show basic app info
            st.write("**Application**")
            st.write(f"Version: 0.5.4+")
            st.write(f"Phase: 5.4+ (Enhanced)")
    
    def render_help(self):
        """Render help information."""
        st.write("**Quick Start:**")
        st.write("1. Select a currency pair and timeframe")
        st.write("2. Click 'Load Data' to fetch historical data")
        st.write("3. Add technical indicators using the indicator panel")
        st.write("4. Analyze the chart and export results")
        
        st.write("**Chart Interaction:**")
        st.write("• **Pan Chart**: Click and drag to move chart")
        st.write("• **Zoom**: Mouse wheel to zoom in/out")
        st.write("• **Price Scaling**: Drag price axis (right side) to scale prices")
        st.write("• **Reset View**: Double-click chart to reset zoom")
        st.write("• **Remove Gaps**: Use chart settings to remove weekend gaps")
        st.write("• **Fullscreen**: Use toolbar button for fullscreen view")
        
        st.write("**Support:**")
        st.write("• Check logs for detailed error information")
        st.write("• Clear cache if experiencing issues")
        st.write("• Refresh page to reset application state")
    
    def run(self):
        """Run the main application."""
        try:
            # Render header
            self.render_header()
            
            # Render sidebar
            self.render_sidebar()
            
            # Render main content
            self.render_main_content()
            
            # Footer
            st.markdown("---")
            st.markdown(
                f"<div style='text-align: center; color: #666; font-size: 0.8rem;'>"
                f"{strings.APP['footer']}"
                "</div>",
                unsafe_allow_html=True
            )
            
        except Exception as e:
            logger.error(f"Error in main app: {str(e)}")
            st.error(get_string("MESSAGES", "application_error", error=str(e)))

            # Show error details in expander
            with st.expander(get_string("MESSAGES", "error_details")):
                st.code(str(e))
                st.write(get_string("MESSAGES", "refresh_page"))


def main():
    """Main entry point for Streamlit app."""
    try:
        app = LionaireApp()
        app.run()
    except Exception as e:
        st.error(f"Failed to start application: {str(e)}")
        st.write("Please check your installation and try again.")


if __name__ == "__main__":
    main()
